"""
Multi-Modal Agent Orchestrator for Enhanced RAG System

This module orchestrates the parallel execution of text and image agents
for multi-modal RAG capabilities, providing intelligent response combination.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Union, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from enum import Enum

from langchain_core.messages import HumanMessage, AIMessage, BaseMessage


class ProcessingMode(Enum):
    """Enumeration of processing modes for the orchestrator."""
    TEXT_ONLY = "text_only"
    IMAGE_ONLY = "image_only"
    MULTIMODAL = "multimodal"


@dataclass
class AgentResult:
    """Container for agent execution results."""
    agent_name: str
    success: bool
    response: Optional[str] = None
    confidence: float = 0.0
    sources: List[Dict] = None
    metadata: Dict[str, Any] = None
    error: Optional[str] = None
    processing_time: float = 0.0

    def __post_init__(self):
        if self.sources is None:
            self.sources = []
        if self.metadata is None:
            self.metadata = {}


@dataclass
class MultiModalInput:
    """Container for multi-modal input data."""
    text: Optional[str] = None
    image_path: Optional[str] = None
    image_type: Optional[str] = None
    chat_history: Optional[str] = None
    session_metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.session_metadata is None:
            self.session_metadata = {}

    @property
    def processing_mode(self) -> ProcessingMode:
        """Determine the processing mode based on available inputs."""
        has_text = bool(self.text and self.text.strip())
        has_image = bool(self.image_path)
        
        if has_text and has_image:
            return ProcessingMode.MULTIMODAL
        elif has_text:
            return ProcessingMode.TEXT_ONLY
        elif has_image:
            return ProcessingMode.IMAGE_ONLY
        else:
            raise ValueError("No valid input provided (neither text nor image)")


class MultiModalOrchestrator:
    """
    Orchestrates parallel execution of text and image agents for multi-modal RAG.
    
    This class coordinates the execution of specialized agents, manages parallel
    processing, and combines responses into coherent final outputs.
    """

    def __init__(self, config):
        """
        Initialize the multi-modal orchestrator.
        
        Args:
            config: Configuration object containing agent settings
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize thread pool for parallel execution
        self.max_workers = getattr(config, 'multimodal_max_workers', 3)
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        
        # Timeout settings
        self.text_agent_timeout = getattr(config, 'text_agent_timeout', 30.0)
        self.image_agent_timeout = getattr(config, 'image_agent_timeout', 45.0)
        self.combination_timeout = getattr(config, 'combination_timeout', 15.0)
        
        # Initialize agents (will be imported when needed to avoid circular imports)
        self._text_rag_agent = None
        self._image_rag_agent = None
        self._web_search_agent = None
        self._response_combiner = None

    def _get_text_rag_agent(self):
        """Lazy initialization of text RAG agent."""
        if self._text_rag_agent is None:
            from .rag_agent import MedicalRAG
            self._text_rag_agent = MedicalRAG(self.config)
        return self._text_rag_agent

    def _get_image_rag_agent(self):
        """Lazy initialization of image RAG agent."""
        if self._image_rag_agent is None:
            from .multimodal_image_agent import MultiModalImageAgent
            self._image_rag_agent = MultiModalImageAgent(self.config)
        return self._image_rag_agent

    def _get_web_search_agent(self):
        """Lazy initialization of web search agent."""
        if self._web_search_agent is None:
            from .web_search_processor_agent import WebSearchProcessorAgent
            self._web_search_agent = WebSearchProcessorAgent(self.config)
        return self._web_search_agent

    def _get_response_combiner(self):
        """Lazy initialization of response combiner."""
        if self._response_combiner is None:
            from .response_combiner import ResponseCombiner
            self._response_combiner = ResponseCombiner(self.config)
        return self._response_combiner

    async def process_multimodal_query(self, input_data: MultiModalInput) -> Dict[str, Any]:
        """
        Process a multi-modal query with parallel agent execution.
        
        Args:
            input_data: Multi-modal input containing text, image, and metadata
            
        Returns:
            Dictionary containing the combined response and metadata
        """
        start_time = time.time()
        processing_mode = input_data.processing_mode
        
        self.logger.info(f"Processing query in {processing_mode.value} mode")
        
        try:
            if processing_mode == ProcessingMode.MULTIMODAL:
                return await self._process_multimodal(input_data)
            elif processing_mode == ProcessingMode.TEXT_ONLY:
                return await self._process_text_only(input_data)
            elif processing_mode == ProcessingMode.IMAGE_ONLY:
                return await self._process_image_only(input_data)
            else:
                raise ValueError(f"Unsupported processing mode: {processing_mode}")
                
        except Exception as e:
            self.logger.error(f"Error in multi-modal processing: {str(e)}")
            return self._create_error_response(str(e), time.time() - start_time)

    async def _process_multimodal(self, input_data: MultiModalInput) -> Dict[str, Any]:
        """Process input with both text and image components."""
        self.logger.info("Starting parallel multi-modal processing")
        
        # Create tasks for parallel execution
        tasks = []
        
        # Text processing task
        if input_data.text:
            text_task = asyncio.create_task(
                self._execute_text_agent(input_data.text, input_data.chat_history)
            )
            tasks.append(("text", text_task))
        
        # Image processing task
        if input_data.image_path:
            image_task = asyncio.create_task(
                self._execute_image_agent(input_data.image_path, input_data.image_type, input_data.text)
            )
            tasks.append(("image", image_task))
        
        # Execute tasks in parallel with timeout
        results = {}
        try:
            # Wait for all tasks to complete or timeout
            for agent_type, task in tasks:
                try:
                    timeout = self.text_agent_timeout if agent_type == "text" else self.image_agent_timeout
                    result = await asyncio.wait_for(task, timeout=timeout)
                    results[agent_type] = result
                    self.logger.info(f"{agent_type.capitalize()} agent completed successfully")
                except asyncio.TimeoutError:
                    self.logger.warning(f"{agent_type.capitalize()} agent timed out")
                    results[agent_type] = self._create_timeout_result(agent_type)
                except Exception as e:
                    self.logger.error(f"{agent_type.capitalize()} agent failed: {str(e)}")
                    results[agent_type] = self._create_error_result(agent_type, str(e))
        
        except Exception as e:
            self.logger.error(f"Error in parallel execution: {str(e)}")
            return self._create_error_response(str(e))
        
        # Check if we need web search fallback for text
        text_result = results.get("text")
        if text_result and not text_result.success and text_result.confidence < self.config.rag.min_retrieval_confidence:
            self.logger.info("Text RAG confidence low, triggering web search fallback")
            try:
                web_search_result = await self._execute_web_search_agent(input_data.text, input_data.chat_history)
                results["web_search"] = web_search_result
            except Exception as e:
                self.logger.error(f"Web search fallback failed: {str(e)}")
        
        # Combine results
        return await self._combine_results(results, input_data)

    async def _process_text_only(self, input_data: MultiModalInput) -> Dict[str, Any]:
        """Process text-only input."""
        self.logger.info("Processing text-only input")
        
        try:
            text_result = await self._execute_text_agent(input_data.text, input_data.chat_history)
            
            # Check for web search fallback
            if not text_result.success or text_result.confidence < self.config.rag.min_retrieval_confidence:
                self.logger.info("Triggering web search fallback for text-only query")
                web_search_result = await self._execute_web_search_agent(input_data.text, input_data.chat_history)
                return self._format_single_result(web_search_result)
            
            return self._format_single_result(text_result)
            
        except Exception as e:
            self.logger.error(f"Error in text-only processing: {str(e)}")
            return self._create_error_response(str(e))

    async def _process_image_only(self, input_data: MultiModalInput) -> Dict[str, Any]:
        """Process image-only input."""
        self.logger.info("Processing image-only input")
        
        try:
            image_result = await self._execute_image_agent(input_data.image_path, input_data.image_type)
            return self._format_single_result(image_result)
            
        except Exception as e:
            self.logger.error(f"Error in image-only processing: {str(e)}")
            return self._create_error_response(str(e))

    async def _execute_text_agent(self, text: str, chat_history: Optional[str] = None) -> AgentResult:
        """Execute the text RAG agent asynchronously."""
        start_time = time.time()

        try:
            # Run in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            rag_agent = self._get_text_rag_agent()

            result = await loop.run_in_executor(
                self.executor,
                lambda: rag_agent.process_query(text, chat_history=chat_history)
            )

            processing_time = time.time() - start_time

            return AgentResult(
                agent_name="TEXT_RAG_AGENT",
                success=True,
                response=result.get("response", ""),
                confidence=result.get("confidence", 0.0),
                sources=result.get("sources", []),
                metadata={"processing_time": processing_time, "original_result": result},
                processing_time=processing_time
            )

        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"Text agent execution failed: {str(e)}")
            return AgentResult(
                agent_name="TEXT_RAG_AGENT",
                success=False,
                error=str(e),
                processing_time=processing_time
            )

    async def _execute_image_agent(self, image_path: str, image_type: Optional[str] = None,
                                 context_text: Optional[str] = None) -> AgentResult:
        """Execute the image RAG agent asynchronously."""
        start_time = time.time()

        try:
            # Run in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            image_agent = self._get_image_rag_agent()

            result = await loop.run_in_executor(
                self.executor,
                lambda: image_agent.process_image(image_path, image_type, context_text)
            )

            processing_time = time.time() - start_time

            return AgentResult(
                agent_name="IMAGE_RAG_AGENT",
                success=True,
                response=result.get("response", ""),
                confidence=result.get("confidence", 0.0),
                sources=result.get("sources", []),
                metadata={
                    "processing_time": processing_time,
                    "image_type": image_type,
                    "original_result": result
                },
                processing_time=processing_time
            )

        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"Image agent execution failed: {str(e)}")
            return AgentResult(
                agent_name="IMAGE_RAG_AGENT",
                success=False,
                error=str(e),
                processing_time=processing_time
            )

    async def _execute_web_search_agent(self, query: str, chat_history: Optional[str] = None) -> AgentResult:
        """Execute the web search agent asynchronously."""
        start_time = time.time()

        try:
            # Run in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            web_search_agent = self._get_web_search_agent()

            result = await loop.run_in_executor(
                self.executor,
                lambda: web_search_agent.process_web_search_results(query, chat_history)
            )

            processing_time = time.time() - start_time

            return AgentResult(
                agent_name="WEB_SEARCH_AGENT",
                success=True,
                response=result if isinstance(result, str) else str(result),
                confidence=0.8,  # Web search typically has good confidence
                sources=[],  # Web search agent handles its own source formatting
                metadata={"processing_time": processing_time},
                processing_time=processing_time
            )

        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"Web search agent execution failed: {str(e)}")
            return AgentResult(
                agent_name="WEB_SEARCH_AGENT",
                success=False,
                error=str(e),
                processing_time=processing_time
            )

    async def _combine_results(self, results: Dict[str, AgentResult], input_data: MultiModalInput) -> Dict[str, Any]:
        """Combine results from multiple agents into a coherent response."""
        start_time = time.time()

        try:
            # Run response combination in thread pool
            loop = asyncio.get_event_loop()
            combiner = self._get_response_combiner()

            combined_response = await asyncio.wait_for(
                loop.run_in_executor(
                    self.executor,
                    lambda: combiner.combine_responses(results, input_data)
                ),
                timeout=self.combination_timeout
            )

            processing_time = time.time() - start_time

            # Determine the primary agent(s) involved
            successful_agents = [name for name, result in results.items() if result.success]
            agent_names = ", ".join(successful_agents) if successful_agents else "MULTIMODAL_ORCHESTRATOR"

            return {
                "status": "success",
                "response": combined_response.get("response", ""),
                "agent_name": agent_names,
                "confidence": combined_response.get("confidence", 0.0),
                "sources": combined_response.get("sources", []),
                "processing_mode": input_data.processing_mode.value,
                "individual_results": {name: self._serialize_result(result) for name, result in results.items()},
                "total_processing_time": processing_time,
                "metadata": combined_response.get("metadata", {})
            }

        except asyncio.TimeoutError:
            self.logger.warning("Response combination timed out, using fallback")
            return self._create_fallback_combined_response(results, input_data)
        except Exception as e:
            self.logger.error(f"Error combining results: {str(e)}")
            return self._create_fallback_combined_response(results, input_data)

    def _format_single_result(self, result: AgentResult) -> Dict[str, Any]:
        """Format a single agent result for return."""
        return {
            "status": "success" if result.success else "error",
            "response": result.response or result.error or "No response generated",
            "agent_name": result.agent_name,
            "confidence": result.confidence,
            "sources": result.sources,
            "processing_mode": "single_agent",
            "processing_time": result.processing_time,
            "metadata": result.metadata
        }

    def _create_timeout_result(self, agent_type: str) -> AgentResult:
        """Create a result object for timed-out agents."""
        return AgentResult(
            agent_name=f"{agent_type.upper()}_AGENT",
            success=False,
            error=f"{agent_type.capitalize()} agent timed out",
            confidence=0.0
        )

    def _create_error_result(self, agent_type: str, error_message: str) -> AgentResult:
        """Create a result object for failed agents."""
        return AgentResult(
            agent_name=f"{agent_type.upper()}_AGENT",
            success=False,
            error=error_message,
            confidence=0.0
        )

    def _create_error_response(self, error_message: str, processing_time: float = 0.0) -> Dict[str, Any]:
        """Create an error response dictionary."""
        return {
            "status": "error",
            "response": f"An error occurred during multi-modal processing: {error_message}",
            "agent_name": "MULTIMODAL_ORCHESTRATOR",
            "confidence": 0.0,
            "sources": [],
            "processing_mode": "error",
            "processing_time": processing_time,
            "error": error_message
        }

    def _create_fallback_combined_response(self, results: Dict[str, AgentResult],
                                         input_data: MultiModalInput) -> Dict[str, Any]:
        """Create a fallback response when combination fails."""
        # Find the best available result
        successful_results = {name: result for name, result in results.items() if result.success}

        if not successful_results:
            return self._create_error_response("All agents failed to process the input")

        # Use the result with highest confidence
        best_result = max(successful_results.values(), key=lambda r: r.confidence)

        return {
            "status": "partial_success",
            "response": f"**Multi-modal processing partially completed.**\n\n{best_result.response}",
            "agent_name": f"MULTIMODAL_ORCHESTRATOR (fallback: {best_result.agent_name})",
            "confidence": best_result.confidence,
            "sources": best_result.sources,
            "processing_mode": input_data.processing_mode.value,
            "individual_results": {name: self._serialize_result(result) for name, result in results.items()},
            "metadata": {"fallback_used": True, "best_agent": best_result.agent_name}
        }

    def _serialize_result(self, result: AgentResult) -> Dict[str, Any]:
        """Serialize an AgentResult for JSON response."""
        return {
            "agent_name": result.agent_name,
            "success": result.success,
            "response": result.response,
            "confidence": result.confidence,
            "sources": result.sources,
            "error": result.error,
            "processing_time": result.processing_time,
            "metadata": result.metadata
        }

    def __del__(self):
        """Cleanup thread pool on destruction."""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)
