"""
Multi-Modal Image Agent for Enhanced RAG System

This module provides specialized image processing capabilities for the multi-modal RAG system,
integrating with existing image analysis agents and providing structured outputs for combination.
"""

import logging
import time
from typing import Dict, List, Optional, Any, Union
from pathlib import Path

from langchain_core.messages import AIMessage


class MultiModalImageAgent:
    """
    Specialized image agent for multi-modal RAG processing.
    
    This agent coordinates with existing image analysis agents and provides
    structured outputs that can be combined with text agent responses.
    """

    def __init__(self, config):
        """
        Initialize the multi-modal image agent.
        
        Args:
            config: Configuration object containing agent settings
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize existing image analysis components
        self._image_analyzer = None
        self._brain_tumor_agent = None
        self._chest_xray_agent = None
        self._skin_lesion_agent = None

    def _get_image_analyzer(self):
        """Lazy initialization of image analyzer."""
        if self._image_analyzer is None:
            from .image_analysis_agent import ImageAnalysisAgent
            self._image_analyzer = ImageAnalysisAgent(self.config)
        return self._image_analyzer

    def _get_brain_tumor_agent(self):
        """Lazy initialization of brain tumor agent."""
        if self._brain_tumor_agent is None:
            from .image_analysis_agent.brain_tumor_agent.brain_tumor_inference import BrainTumorInference
            self._brain_tumor_agent = BrainTumorInference()
        return self._brain_tumor_agent

    def process_image(self, image_path: str, image_type: Optional[str] = None, 
                     context_text: Optional[str] = None) -> Dict[str, Any]:
        """
        Process an image with context awareness for multi-modal scenarios.
        
        Args:
            image_path: Path to the image file
            image_type: Type of medical image (if known)
            context_text: Associated text context for enhanced analysis
            
        Returns:
            Dictionary containing structured image analysis results
        """
        start_time = time.time()
        
        try:
            # Validate image path
            if not Path(image_path).exists():
                raise FileNotFoundError(f"Image file not found: {image_path}")
            
            # Determine image type if not provided
            if not image_type:
                image_type = self._classify_image_type(image_path)
            
            self.logger.info(f"Processing {image_type} image: {image_path}")
            
            # Route to appropriate specialized agent
            if image_type == "brain_mri":
                return self._process_brain_mri(image_path, context_text)
            elif image_type == "chest_xray":
                return self._process_chest_xray(image_path, context_text)
            elif image_type == "skin_lesion":
                return self._process_skin_lesion(image_path, context_text)
            else:
                return self._process_general_medical_image(image_path, image_type, context_text)
                
        except Exception as e:
            self.logger.error(f"Error processing image: {str(e)}")
            return self._create_error_response(str(e), time.time() - start_time)

    def _classify_image_type(self, image_path: str) -> str:
        """Classify the type of medical image."""
        try:
            analyzer = self._get_image_analyzer()
            result = analyzer.analyze_image(image_path)
            return result.get('image_type', 'unknown')
        except Exception as e:
            self.logger.warning(f"Image classification failed: {str(e)}")
            return 'unknown'

    def _process_brain_mri(self, image_path: str, context_text: Optional[str] = None) -> Dict[str, Any]:
        """Process brain MRI images with enhanced context awareness."""
        try:
            brain_agent = self._get_brain_tumor_agent()
            analysis_results = brain_agent.analyze_mri(image_path)
            
            # Check for errors in analysis
            if 'error' in analysis_results:
                return self._create_error_response(analysis_results['error'])
            
            # Format response with context awareness
            confidence = analysis_results['confidence']
            has_tumor = analysis_results['has_tumor']
            
            # Create structured response
            response_data = {
                "image_type": "brain_mri",
                "analysis_type": "tumor_detection",
                "findings": {
                    "tumor_detected": has_tumor,
                    "confidence": confidence,
                    "tumor_type": analysis_results.get('tumor_type'),
                    "class_probabilities": analysis_results.get('class_probabilities', {})
                },
                "clinical_interpretation": self._generate_brain_mri_interpretation(analysis_results, context_text),
                "recommendations": analysis_results.get('recommendation', ''),
                "requires_validation": True
            }
            
            # Generate narrative response
            narrative = self._create_brain_mri_narrative(response_data, context_text)
            
            return {
                "response": narrative,
                "confidence": confidence,
                "sources": [{"type": "ai_analysis", "content": "Brain MRI AI Analysis"}],
                "structured_data": response_data,
                "metadata": {
                    "image_path": image_path,
                    "analysis_type": "brain_tumor_detection",
                    "requires_human_validation": True
                }
            }
            
        except Exception as e:
            self.logger.error(f"Brain MRI processing failed: {str(e)}")
            return self._create_error_response(str(e))

    def _process_chest_xray(self, image_path: str, context_text: Optional[str] = None) -> Dict[str, Any]:
        """Process chest X-ray images with enhanced context awareness."""
        try:
            analyzer = self._get_image_analyzer()
            predicted_class = analyzer.classify_chest_xray(image_path)
            
            # Create structured response
            response_data = {
                "image_type": "chest_xray",
                "analysis_type": "covid_detection",
                "findings": {
                    "classification": predicted_class,
                    "covid_detected": predicted_class == "covid19",
                    "confidence": 0.85  # Default confidence for chest X-ray classification
                },
                "clinical_interpretation": self._generate_chest_xray_interpretation(predicted_class, context_text),
                "recommendations": self._get_chest_xray_recommendations(predicted_class),
                "requires_validation": True
            }
            
            # Generate narrative response
            narrative = self._create_chest_xray_narrative(response_data, context_text)
            
            return {
                "response": narrative,
                "confidence": 0.85,
                "sources": [{"type": "ai_analysis", "content": "Chest X-ray AI Analysis"}],
                "structured_data": response_data,
                "metadata": {
                    "image_path": image_path,
                    "analysis_type": "covid_detection",
                    "requires_human_validation": True
                }
            }
            
        except Exception as e:
            self.logger.error(f"Chest X-ray processing failed: {str(e)}")
            return self._create_error_response(str(e))

    def _process_skin_lesion(self, image_path: str, context_text: Optional[str] = None) -> Dict[str, Any]:
        """Process skin lesion images with enhanced context awareness."""
        try:
            analyzer = self._get_image_analyzer()
            segmentation_result = analyzer.segment_skin_lesion(image_path)
            
            # Create structured response
            response_data = {
                "image_type": "skin_lesion",
                "analysis_type": "lesion_segmentation",
                "findings": {
                    "segmentation_successful": bool(segmentation_result),
                    "confidence": 0.8 if segmentation_result else 0.0
                },
                "clinical_interpretation": self._generate_skin_lesion_interpretation(segmentation_result, context_text),
                "recommendations": self._get_skin_lesion_recommendations(segmentation_result),
                "requires_validation": True
            }
            
            # Generate narrative response
            narrative = self._create_skin_lesion_narrative(response_data, context_text)
            
            return {
                "response": narrative,
                "confidence": 0.8 if segmentation_result else 0.0,
                "sources": [{"type": "ai_analysis", "content": "Skin Lesion AI Analysis"}],
                "structured_data": response_data,
                "metadata": {
                    "image_path": image_path,
                    "analysis_type": "lesion_segmentation",
                    "requires_human_validation": True,
                    "segmentation_output": self.config.medical_cv.skin_lesion_segmentation_output_path if segmentation_result else None
                }
            }
            
        except Exception as e:
            self.logger.error(f"Skin lesion processing failed: {str(e)}")
            return self._create_error_response(str(e))

    def _process_general_medical_image(self, image_path: str, image_type: str, 
                                     context_text: Optional[str] = None) -> Dict[str, Any]:
        """Process general medical images when specific agents are not available."""
        try:
            # For unknown or general medical images, provide basic analysis
            response_data = {
                "image_type": image_type,
                "analysis_type": "general_medical_image",
                "findings": {
                    "image_processed": True,
                    "specialized_analysis_available": False
                },
                "clinical_interpretation": f"This appears to be a {image_type} medical image. Specialized AI analysis is not currently available for this image type.",
                "recommendations": "Please consult with a qualified healthcare professional for proper interpretation of this medical image.",
                "requires_validation": True
            }
            
            narrative = f"""**Medical Image Analysis Results:**

🔍 **Image Type**: {image_type.replace('_', ' ').title()}
📊 **Analysis Status**: General processing completed
📝 **Interpretation**: {response_data['clinical_interpretation']}

⚠️ **Important Note**: Specialized AI analysis is not available for this image type. Please consult with a qualified healthcare professional for proper medical interpretation.

🏥 **Recommendation**: {response_data['recommendations']}"""

            if context_text:
                narrative += f"\n\n**Context Consideration**: The provided text context has been noted and may be relevant for professional medical evaluation."
            
            return {
                "response": narrative,
                "confidence": 0.5,  # Lower confidence for general processing
                "sources": [{"type": "general_analysis", "content": "General Medical Image Processing"}],
                "structured_data": response_data,
                "metadata": {
                    "image_path": image_path,
                    "analysis_type": "general_medical_image",
                    "requires_human_validation": True
                }
            }
            
        except Exception as e:
            self.logger.error(f"General image processing failed: {str(e)}")
            return self._create_error_response(str(e))

    def _generate_brain_mri_interpretation(self, analysis_results: Dict, context_text: Optional[str] = None) -> str:
        """Generate clinical interpretation for brain MRI results."""
        has_tumor = analysis_results['findings']['tumor_detected']
        confidence = analysis_results['findings']['confidence']
        
        if has_tumor:
            interpretation = f"AI analysis indicates potential tumor presence with {confidence*100:.1f}% confidence. "
            if analysis_results['findings'].get('tumor_type'):
                interpretation += f"Detected tumor type: {analysis_results['findings']['tumor_type']}. "
        else:
            interpretation = f"AI analysis suggests no tumor detected with {confidence*100:.1f}% confidence. "
        
        if context_text:
            interpretation += f"Given the provided context, this analysis should be correlated with clinical symptoms and history."
        
        return interpretation

    def _generate_chest_xray_interpretation(self, classification: str, context_text: Optional[str] = None) -> str:
        """Generate clinical interpretation for chest X-ray results."""
        if classification == "covid19":
            interpretation = "AI analysis indicates findings potentially consistent with COVID-19 pneumonia patterns. "
        elif classification == "normal":
            interpretation = "AI analysis suggests normal chest X-ray findings with no obvious COVID-19 patterns detected. "
        else:
            interpretation = "AI analysis was inconclusive. Image quality or content may not be suitable for reliable analysis. "
        
        if context_text:
            interpretation += "Clinical correlation with symptoms and exposure history is essential for proper diagnosis."
        
        return interpretation

    def _generate_skin_lesion_interpretation(self, segmentation_result: bool, context_text: Optional[str] = None) -> str:
        """Generate clinical interpretation for skin lesion results."""
        if segmentation_result:
            interpretation = "AI successfully segmented the skin lesion, providing boundary detection for clinical assessment. "
        else:
            interpretation = "AI was unable to segment the skin lesion. This may be due to image quality, lesion characteristics, or technical limitations. "
        
        if context_text:
            interpretation += "Clinical examination by a dermatologist remains essential for proper diagnosis and treatment planning."
        
        return interpretation

    def _get_chest_xray_recommendations(self, classification: str) -> str:
        """Get recommendations based on chest X-ray classification."""
        if classification == "covid19":
            return "Immediate medical consultation recommended. Consider RT-PCR testing and follow local health guidelines for isolation and treatment."
        elif classification == "normal":
            return "While X-ray appears normal, clinical symptoms should still be evaluated by a healthcare professional if COVID-19 is suspected."
        else:
            return "Please upload a clearer chest X-ray image or consult with a healthcare professional for proper evaluation."

    def _get_skin_lesion_recommendations(self, segmentation_result: bool) -> str:
        """Get recommendations based on skin lesion segmentation."""
        if segmentation_result:
            return "Consult a dermatologist for professional evaluation. Use the segmentation results as reference for tracking changes over time."
        else:
            return "Consider retaking the image with better lighting and focus, then consult a dermatologist for professional evaluation."

    def _create_brain_mri_narrative(self, response_data: Dict, context_text: Optional[str] = None) -> str:
        """Create narrative response for brain MRI analysis."""
        findings = response_data['findings']
        confidence = findings['confidence'] * 100

        if findings['tumor_detected']:
            narrative = f"""**Brain MRI Analysis Results:**

🔍 **Tumor Detection**: POSITIVE
📋 **Tumor Type**: {findings.get('tumor_type', 'Unspecified')}
📊 **Confidence**: {confidence:.1f}%

📈 **Detailed Probabilities**:"""

            for class_name, prob in findings.get('class_probabilities', {}).items():
                narrative += f"\n- {class_name}: {prob*100:.1f}%"

            narrative += f"""

📝 **Clinical Interpretation**: {response_data['clinical_interpretation']}
📋 **Recommendations**: {response_data['recommendations']}

⚠️ **Important Note**: This is an AI-assisted analysis. Please consult with a medical professional for proper diagnosis and treatment."""
        else:
            narrative = f"""**Brain MRI Analysis Results:**

🔍 **Tumor Detection**: NEGATIVE
📊 **Confidence**: {confidence:.1f}%
📝 **Clinical Interpretation**: {response_data['clinical_interpretation']}

ℹ️ **Note**: While no tumor was detected, regular medical check-ups are still recommended."""

        if context_text:
            narrative += f"\n\n**Context Consideration**: The analysis has been performed considering the provided context: \"{context_text[:100]}...\""

        return narrative

    def _create_chest_xray_narrative(self, response_data: Dict, context_text: Optional[str] = None) -> str:
        """Create narrative response for chest X-ray analysis."""
        findings = response_data['findings']
        classification = findings['classification']

        if classification == "covid19":
            narrative = f"""**Chest X-ray Analysis Results:**

🔍 **COVID-19 Detection**: POSITIVE
📊 **Result**: The analysis indicates findings potentially consistent with COVID-19
📝 **Clinical Interpretation**: {response_data['clinical_interpretation']}

⚠️ **Important Medical Disclaimer**:
- This is an AI-assisted analysis and NOT a definitive medical diagnosis
- COVID-19 diagnosis requires clinical correlation with symptoms, exposure history, and laboratory tests (RT-PCR, antigen tests)
- Many conditions can cause similar X-ray findings
- Please consult with a qualified healthcare professional immediately for proper evaluation, additional testing, and appropriate medical management
- If you have symptoms or suspect COVID-19 exposure, follow local health guidelines for testing and isolation

📋 **Recommendations**: {response_data['recommendations']}"""

        elif classification == "normal":
            narrative = f"""**Chest X-ray Analysis Results:**

🔍 **COVID-19 Detection**: NEGATIVE
📊 **Result**: The analysis indicates NORMAL chest X-ray findings
📝 **Clinical Interpretation**: {response_data['clinical_interpretation']}

ℹ️ **Important Notes**:
- A normal chest X-ray does not completely rule out COVID-19, especially in early stages or mild cases
- Many COVID-19 patients have normal chest X-rays, particularly in the early phase of infection
- Clinical symptoms and laboratory tests (RT-PCR, antigen tests) are more reliable for COVID-19 diagnosis

⚠️ **Medical Disclaimer**:
- This is an AI-assisted analysis and should not replace professional medical evaluation
- If you have COVID-19 symptoms or exposure concerns, please consult with a healthcare professional and consider appropriate testing
- Follow local health guidelines regardless of this X-ray analysis result

📋 **Recommendations**: {response_data['recommendations']}"""

        else:
            narrative = f"""**Chest X-ray Analysis Results:**

❌ **Analysis Status**: INCONCLUSIVE
📝 **Issue**: The uploaded image is not clear enough for reliable analysis or may not be a valid chest X-ray image

🔧 **Recommendations**:
- Please ensure the image is a clear, high-quality chest X-ray
- The image should be properly oriented and well-lit
- Avoid blurry, cropped, or low-resolution images

⚠️ **Next Steps**: Please upload a clearer chest X-ray image or consult with a healthcare professional for proper medical evaluation."""

        if context_text:
            narrative += f"\n\n**Context Consideration**: The analysis has been performed considering the provided context: \"{context_text[:100]}...\""

        return narrative

    def _create_skin_lesion_narrative(self, response_data: Dict, context_text: Optional[str] = None) -> str:
        """Create narrative response for skin lesion analysis."""
        findings = response_data['findings']

        if findings['segmentation_successful']:
            narrative = f"""**Skin Lesion Analysis Results:**

🔍 **Segmentation Status**: SUCCESSFUL
📊 **Analysis Type**: Automated lesion boundary detection and segmentation
📝 **Clinical Interpretation**: {response_data['clinical_interpretation']}

🎯 **What This Analysis Shows**:
- **Lesion Boundaries**: The highlighted areas show the detected boundaries of the skin lesion
- **Segmentation Mask**: The overlay indicates the precise location and extent of the lesion
- **Spatial Analysis**: This helps assess lesion size, shape, and border characteristics

📋 **Clinical Relevance**:
- **Border Assessment**: Irregular or asymmetric borders may indicate concern
- **Size Measurement**: Accurate lesion dimensions for monitoring changes over time
- **Shape Analysis**: Helps evaluate lesion morphology and growth patterns
- **Documentation**: Provides baseline for future comparison and monitoring

⚠️ **Important Medical Disclaimer**:
- This is an AI-assisted **segmentation tool**, NOT a diagnostic system
- Segmentation does **NOT** determine if a lesion is benign or malignant
- This analysis **CANNOT** replace dermatological examination or biopsy
- Any concerning skin lesions require evaluation by a qualified dermatologist
- Changes in size, color, shape, or texture should be evaluated promptly by a healthcare professional

🏥 **Next Steps**: {response_data['recommendations']}

📸 **Segmented Image**: The processed image with lesion boundaries is available for download and clinical reference."""

        else:
            narrative = f"""**Skin Lesion Analysis Results:**

❌ **Segmentation Status**: UNSUCCESSFUL
📝 **Issue**: Unable to detect or segment skin lesion in the uploaded image

🔧 **Possible Reasons**:
- Image quality may be insufficient (blurry, low resolution, poor lighting)
- The image may not contain a clear skin lesion
- Lesion may be too small or faint for automated detection
- Image orientation or cropping may affect analysis

💡 **Recommendations for Better Results**:
- Ensure good lighting and clear focus when taking the photo
- Capture the lesion with adequate surrounding normal skin for context
- Use high resolution and avoid excessive zoom or cropping
- Take the photo perpendicular to the skin surface
- Ensure the lesion is clearly visible and well-contrasted

⚠️ **Important Note**:
- Inability to segment does not mean the lesion is normal or abnormal
- Some lesions may be difficult for automated systems to detect
- **Always consult a dermatologist** for proper evaluation of any skin concerns
- Do not rely solely on automated analysis for medical decisions

🏥 **Next Steps**: {response_data['recommendations']}"""

        if context_text:
            narrative += f"\n\n**Context Consideration**: The analysis has been performed considering the provided context: \"{context_text[:100]}...\""

        return narrative

    def _create_error_response(self, error_message: str, processing_time: float = 0.0) -> Dict[str, Any]:
        """Create an error response for image processing failures."""
        return {
            "response": f"An error occurred during image analysis: {error_message}",
            "confidence": 0.0,
            "sources": [],
            "structured_data": {
                "error": True,
                "error_message": error_message
            },
            "metadata": {
                "processing_time": processing_time,
                "status": "error"
            }
        }
