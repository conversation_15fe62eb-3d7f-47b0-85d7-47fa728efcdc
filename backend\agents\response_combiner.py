"""
Response Combiner for Multi-Modal RAG System

This module provides intelligent combination of responses from multiple agents
(text RAG, image analysis, web search) into coherent, comprehensive outputs.
"""

import logging
import time
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage


class ResponseCombiner:
    """
    Combines responses from multiple agents into coherent final outputs.
    
    This class uses advanced prompting strategies and LLM capabilities to
    intelligently merge text and image analysis results with proper context.
    """

    def __init__(self, config):
        """
        Initialize the response combiner.
        
        Args:
            config: Configuration object containing LLM settings
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize LLM for response combination
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash-exp",
            temperature=0.3,  # Lower temperature for more consistent combination
            google_api_key=config.google_api_key
        )

    def combine_responses(self, agent_results: Dict[str, Any], input_data: Any) -> Dict[str, Any]:
        """
        Combine responses from multiple agents into a coherent final response.
        
        Args:
            agent_results: Dictionary of agent results keyed by agent type
            input_data: Original input data for context
            
        Returns:
            Dictionary containing the combined response and metadata
        """
        start_time = time.time()
        
        try:
            # Filter successful results
            successful_results = {
                name: result for name, result in agent_results.items() 
                if hasattr(result, 'success') and result.success
            }
            
            if not successful_results:
                return self._create_fallback_response("No successful agent results to combine")
            
            # Determine combination strategy based on available results
            if len(successful_results) == 1:
                return self._format_single_agent_result(list(successful_results.values())[0])
            
            # Multi-agent combination
            if "text" in successful_results and "image" in successful_results:
                return self._combine_text_and_image(successful_results, input_data)
            elif "text" in successful_results and "web_search" in successful_results:
                return self._combine_text_and_web_search(successful_results, input_data)
            elif "image" in successful_results and "web_search" in successful_results:
                return self._combine_image_and_web_search(successful_results, input_data)
            else:
                return self._combine_general_results(successful_results, input_data)
                
        except Exception as e:
            self.logger.error(f"Error in response combination: {str(e)}")
            return self._create_fallback_response(f"Combination failed: {str(e)}")

    def _combine_text_and_image(self, results: Dict[str, Any], input_data: Any) -> Dict[str, Any]:
        """Combine text RAG and image analysis results."""
        text_result = results["text"]
        image_result = results["image"]
        
        # Create combination prompt
        system_prompt = """You are a medical AI assistant specializing in combining text-based medical knowledge with image analysis results. Your task is to create a comprehensive, coherent response that integrates both text and image findings.

Guidelines:
1. Synthesize information from both text and image analysis
2. Highlight correlations and complementary findings
3. Maintain medical accuracy and appropriate disclaimers
4. Structure the response clearly with distinct sections
5. Emphasize the importance of professional medical consultation
6. Use clear, professional medical language
7. Include confidence assessments when relevant"""

        user_prompt = f"""Please combine the following medical analysis results into a comprehensive response:

**Original Query Context:**
Text: {getattr(input_data, 'text', 'N/A')}
Image: {getattr(input_data, 'image_path', 'Medical image provided')}

**Text-Based Medical Knowledge (RAG Analysis):**
Response: {text_result.response}
Confidence: {text_result.confidence}
Sources: {len(text_result.sources)} medical documents

**Image Analysis Results:**
Response: {image_result.response}
Confidence: {image_result.confidence}
Analysis Type: {image_result.metadata.get('analysis_type', 'Unknown')}

**Task:**
Create a unified medical response that:
1. Integrates both text knowledge and image findings
2. Identifies correlations between the two analyses
3. Provides a comprehensive clinical perspective
4. Maintains appropriate medical disclaimers
5. Suggests next steps for the patient

Format the response professionally with clear sections and appropriate medical terminology."""

        try:
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            combined_response = self.llm.invoke(messages)
            
            # Calculate combined confidence
            combined_confidence = (text_result.confidence + image_result.confidence) / 2
            
            # Merge sources
            combined_sources = text_result.sources + image_result.sources
            
            return {
                "response": combined_response.content,
                "confidence": combined_confidence,
                "sources": combined_sources,
                "metadata": {
                    "combination_type": "text_and_image",
                    "text_confidence": text_result.confidence,
                    "image_confidence": image_result.confidence,
                    "processing_time": time.time() - time.time()
                }
            }
            
        except Exception as e:
            self.logger.error(f"LLM combination failed: {str(e)}")
            return self._create_manual_combination(results, "text_and_image")

    def _combine_text_and_web_search(self, results: Dict[str, Any], input_data: Any) -> Dict[str, Any]:
        """Combine text RAG and web search results."""
        text_result = results["text"]
        web_result = results["web_search"]
        
        system_prompt = """You are a medical AI assistant combining internal medical knowledge with current web-based medical information. Create a comprehensive response that leverages both sources appropriately.

Guidelines:
1. Prioritize established medical knowledge from internal sources
2. Use web information to supplement and provide current context
3. Clearly distinguish between established knowledge and current information
4. Maintain medical accuracy and appropriate disclaimers
5. Structure the response with clear source attribution"""

        user_prompt = f"""Combine these medical information sources:

**Query:** {getattr(input_data, 'text', 'Medical query')}

**Internal Medical Knowledge:**
{text_result.response}
Confidence: {text_result.confidence}

**Current Web Information:**
{web_result.response}

Create a comprehensive medical response that appropriately combines both sources."""

        try:
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            combined_response = self.llm.invoke(messages)
            
            # Web search typically has higher confidence when RAG fails
            combined_confidence = max(text_result.confidence, 0.8)
            
            return {
                "response": combined_response.content,
                "confidence": combined_confidence,
                "sources": text_result.sources,
                "metadata": {
                    "combination_type": "text_and_web_search",
                    "fallback_used": True
                }
            }
            
        except Exception as e:
            self.logger.error(f"Text+Web combination failed: {str(e)}")
            return self._create_manual_combination(results, "text_and_web_search")

    def _combine_image_and_web_search(self, results: Dict[str, Any], input_data: Any) -> Dict[str, Any]:
        """Combine image analysis and web search results."""
        image_result = results["image"]
        web_result = results["web_search"]
        
        # For image + web search, create a structured combination
        combined_response = f"""**Multi-Modal Medical Analysis Results**

**Image Analysis:**
{image_result.response}

**Related Medical Information:**
{web_result.response}

**Integrated Assessment:**
The image analysis provides specific findings about the uploaded medical image, while the web search provides broader medical context. Both sources of information should be considered together with professional medical evaluation for comprehensive assessment.

**Important Note:** This analysis combines AI-based image processing with current medical information. Professional medical consultation remains essential for proper diagnosis and treatment planning."""

        combined_confidence = (image_result.confidence + 0.8) / 2  # Web search assumed 0.8 confidence
        
        return {
            "response": combined_response,
            "confidence": combined_confidence,
            "sources": image_result.sources,
            "metadata": {
                "combination_type": "image_and_web_search",
                "image_confidence": image_result.confidence
            }
        }

    def _combine_general_results(self, results: Dict[str, Any], input_data: Any) -> Dict[str, Any]:
        """General combination for any set of results."""
        # Create a structured combination of all available results
        combined_response = "**Multi-Agent Medical Analysis Results**\n\n"
        
        total_confidence = 0
        source_count = 0
        all_sources = []
        
        for agent_name, result in results.items():
            combined_response += f"**{agent_name.replace('_', ' ').title()} Analysis:**\n"
            combined_response += f"{result.response}\n\n"
            
            total_confidence += result.confidence
            source_count += 1
            all_sources.extend(result.sources)
        
        combined_response += """**Integrated Assessment:**
Multiple AI agents have analyzed your query from different perspectives. Each analysis provides valuable insights that should be considered together. Professional medical consultation is recommended for comprehensive evaluation and treatment planning."""
        
        avg_confidence = total_confidence / source_count if source_count > 0 else 0.0
        
        return {
            "response": combined_response,
            "confidence": avg_confidence,
            "sources": all_sources,
            "metadata": {
                "combination_type": "general_multi_agent",
                "agent_count": source_count
            }
        }

    def _format_single_agent_result(self, result: Any) -> Dict[str, Any]:
        """Format a single agent result for consistency."""
        return {
            "response": result.response,
            "confidence": result.confidence,
            "sources": result.sources,
            "metadata": {
                "combination_type": "single_agent",
                "agent_name": result.agent_name
            }
        }

    def _create_manual_combination(self, results: Dict[str, Any], combination_type: str) -> Dict[str, Any]:
        """Create a manual combination when LLM fails."""
        combined_response = f"**{combination_type.replace('_', ' ').title()} Analysis Results**\n\n"
        
        total_confidence = 0
        all_sources = []
        
        for agent_name, result in results.items():
            combined_response += f"**{agent_name.replace('_', ' ').title()}:**\n{result.response}\n\n"
            total_confidence += result.confidence
            all_sources.extend(result.sources)
        
        avg_confidence = total_confidence / len(results) if results else 0.0
        
        return {
            "response": combined_response,
            "confidence": avg_confidence,
            "sources": all_sources,
            "metadata": {
                "combination_type": combination_type,
                "manual_combination": True
            }
        }

    def _create_fallback_response(self, error_message: str) -> Dict[str, Any]:
        """Create a fallback response when combination fails."""
        return {
            "response": f"Unable to combine agent responses: {error_message}",
            "confidence": 0.0,
            "sources": [],
            "metadata": {
                "combination_type": "fallback",
                "error": error_message
            }
        }
